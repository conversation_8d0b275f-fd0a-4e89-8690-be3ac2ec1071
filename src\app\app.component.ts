import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { initializeApp } from '@firebase/app';
import { getDatabase } from '@firebase/database';
import { TranslateService } from '@ngx-translate/core';
import { environment } from 'src/environments/environment';
import { BaseConstantModel } from './core/ng-model/base-constant-model';
import { LanguageService } from './core/services/language-services/language.service';
import { IUser } from './core/interfaces/auth-interfaces/iuser-model';
import { CallModalService } from './shared/components/calles/components/incoming-call/call-modal.service';
import { MatDialog } from '@angular/material/dialog';
import { IncomingCallComponent } from './shared/components/calles/components/incoming-call/incoming-call.component';
import { AudioType } from './core/enums/audio-type.enum';
import { SocketService } from './shared/components/calles/services/socket-services/socket.service';
import { RingtoneService } from './shared/components/calles/services/ringtone-services/ringtone.service';
import { SocketConnectionService } from './shared/components/calles/services/socket-connection/socket-connection.service';
import { CallHandlingService } from './shared/components/calles/services/call-handling/call-handling.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  title = 'huffaz-app';

  constructor(
    public translate: TranslateService,
    private router: Router,
    private langService: LanguageService,
    private socketConnectionService: SocketConnectionService,
    private callHandlingService: CallHandlingService
  ) {}

  ngOnInit(): void {
    this.langService.initLang();
    this.socketConnectionService.connect();
    this.callHandlingService.listenToSocketEvents();

    const app = initializeApp(environment.firebaseConfig);
    getDatabase(app);
  }

  showHeader(): boolean {
    return (
      !BaseConstantModel.NO_HEADER_ROUTES.includes(
        this.router.url.split('?')[0]
      ) && this.router.url !== '/'
    );
  }
}
