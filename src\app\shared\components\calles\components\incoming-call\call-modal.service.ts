import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { IncomingCallComponent } from './incoming-call.component';

@Injectable({ providedIn: 'root' })
export class CallModalService {
  constructor(private dialog: MatDialog) {}

  open(callerName: string, userId: number) {
    return this.dialog.open(IncomingCallComponent, {
      data: { callerName, userId },
      disableClose: true,
      panelClass: 'incoming-call-dialog',
    });
  }
}
