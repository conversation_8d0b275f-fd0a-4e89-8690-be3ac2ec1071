import { Injectable } from '@angular/core';
import { AudioType } from 'src/app/core/enums/audio-type.enum';

@Injectable({ providedIn: 'root' })
export class RingtoneService {
  private audios = {
    [AudioType.Ringing]: new Audio('assets/audios/ringing1.mp3'),
    [AudioType.Calling]: new Audio('assets/audios/ringtone.mp3')
  };

  constructor() {
    Object.values(this.audios).forEach(audio => {
      audio.loop = true;
      audio.preload = 'auto';
    });
  }

  play(type: AudioType): void {
    this.stopAll();
    this.audios[type].play().catch(err => console.error(`Play failed:`, err));
  }

  stop(type: AudioType): void {
    const audio = this.audios[type];
    audio.pause();
    audio.currentTime = 0;
  }

  stopAll(): void {
    Object.values(this.audios).forEach(audio => {
      audio.pause();
      audio.currentTime = 0;
    });
  }
}
