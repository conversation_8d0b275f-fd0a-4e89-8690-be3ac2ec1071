import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AudioType } from 'src/app/core/enums/audio-type.enum';
import { IncomingCallComponent } from '../../components/incoming-call/incoming-call.component';
import { RingtoneService } from '../ringtone-services/ringtone.service';
import { SocketConnectionService } from '../socket-connection/socket-connection.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CallHandlingService {
   private callAcceptedSubject = new BehaviorSubject<any | null>(null);

  callAccepted$ = this.callAcceptedSubject.asObservable();
  constructor(
    private dialog: MatDialog,
    private ringtoneService: RingtoneService,
    private socketConnectionService: SocketConnectionService
  ) {}

  listenToSocketEvents(): void {
    const socket = this.socketConnectionService.getSocket();

    socket.on('receiving_call', (data: any) => {
      console.log('📞 receiving_call:', data);

      this.ringtoneService.play(AudioType.Ringing);

      this.dialog
        .open(IncomingCallComponent, {
          data,
          disableClose: true,
          panelClass: 'incoming-call-dialog',
          hasBackdrop: false,
          position: {
            bottom: '20px',
            right: '20px',
          },
          width: '300px',
        })
        .afterClosed()
        .subscribe((result) => {
          this.ringtoneService.stop(AudioType.Ringing);
          if (result === 'accepted') {
            socket.emit('call_accepted', data);
          } else if (result === 'rejected') {
            socket.emit('call_rejected', { data });
          }
        });
    });
    socket.on('call_accepted', (data: any) => {
      console.log('✅ Call accepted:', data);

      this.ringtoneService.stop(AudioType.Calling); 
      this.dialog.closeAll();
          this.callAcceptedSubject.next(data);

    });

    socket.on('call_rejected', () => {
      this.ringtoneService.stop(AudioType.Calling);
      this.dialog.closeAll();
    });

    socket.on('call_not_answered', () => {
      this.ringtoneService.stop(AudioType.Calling);
      this.dialog.closeAll();
    });

    socket.on('disconnect', () => {
      console.warn('⚠️ Socket disconnected');
    });
  }
}
