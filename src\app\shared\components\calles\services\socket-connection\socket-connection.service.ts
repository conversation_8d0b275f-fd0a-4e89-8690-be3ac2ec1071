import { Injectable } from '@angular/core';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { SocketService } from 'src/app/shared/components/calles/services/socket-services/socket.service';

@Injectable({ providedIn: 'root' })
export class SocketConnectionService {
  constructor(private socketService: SocketService) {}

  connect(): void {
    const currentUser: IUser = JSON.parse(localStorage.getItem('user') || '{}');

    this.socketService.on('connect', () => {
      const socket = this.socketService.getSocket();
      socket.emit('identify-user', {
        userId: currentUser.id,
        socketId: socket.id,
      });
      socket.emit('joinRoom', 'GeneralRoom');
    });
  }

  getSocket() {
    return this.socketService.getSocket();
  }
}
