import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AudioType } from 'src/app/core/enums/audio-type.enum';
import { CallStatusEnum } from 'src/app/core/enums/call-status-enum.enum';
import { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { ICreateCallRoom } from 'src/app/core/interfaces/calls/icreate-room-call';
import { IStartCallSessionRequest } from 'src/app/core/interfaces/calls/istart-call-session-request';
import { IProgTasmeeaTskCallConnector } from 'src/app/core/interfaces/calls/prog-tasks/iprog-tasmeea-tsk-call-connector';
import { BaseResponseModel } from 'src/app/core/ng-model/base-response-model';
import { CallsService } from 'src/app/core/services/calls-services/calls.service';
import { RingtoneService } from '../../services/ringtone-services/ringtone.service';
import { SocketService } from '../../services/socket-services/socket.service';


@Component({
  selector: 'app-calling-dialog',
  templateUrl: './calling-dialog.component.html',
  styleUrls: ['./calling-dialog.component.scss'],
})
export class CallingDialogComponent implements OnInit {
  currentUser: IUser | undefined;

  constructor(
    public dialogRef: MatDialogRef<CallingDialogComponent>,
    private callService: CallsService,
    private ringtoneService: RingtoneService,
    private socketService: SocketService,
    @Inject(MAT_DIALOG_DATA)
    public data: { callData: IProgTasmeeaTskCallConnector; role: any }
  ) {}

  ngOnInit(): void {
    this.currentUser = JSON.parse(localStorage.getItem('user') || '{}');
    this.startCall();
  }

  startCall(): void {
    const techInfo = this.data.callData;
    const payload: IStartCallSessionRequest = {
      modrId: this.currentUser?.id,
      toUserId: techInfo.availableTechInfo?.usrId,
      callModuleTypehuffazId: this.data.role,
      dayId: techInfo.tasmeeaTskFullDetails?.dayTask,
      tskId: techInfo.tasmeeaTskFullDetails?.id,
      callModuleEntityId: techInfo.tasmeeaTskFullDetails?.id,
      isVideoMute: techInfo.isVideoMute,
      batId: techInfo.batId,
    };
    this.ringtoneService.play(AudioType.Calling);
    this.callService.startCallSession(payload).subscribe(
      (res: BaseResponseModel) => {
        if (res.data) {
          let callRoom: ICreateCallRoom = {
            roomId: res.data.socketRoom,
            modrId: this.currentUser?.id || '',
            toUserId: techInfo.availableTechInfo?.usrId || '',
          };
          this.socketService.emit('start_call', callRoom);
        } else this.cancelCall(CallStatusEnum.Failed);
      },
      (error) => {
        this.cancelCall(CallStatusEnum.Failed);
      }
    );
  }

  cancelCall(callEndingStatus?:CallStatusEnum): void {
    this.socketService.emit('cancel_call', { userId: this.currentUser?.id });
    this.dialogRef.close(callEndingStatus || CallStatusEnum.Ended);
    this.ringtoneService.stop(AudioType.Calling);
  }
}
