import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AudioType } from 'src/app/core/enums/audio-type.enum';
import { RingtoneService } from '../../services/ringtone-services/ringtone.service';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.scss'],
})
export class IncomingCallComponent {
  constructor(
    private ringtoneService: RingtoneService,
    private dialogRef: MatDialogRef<IncomingCallComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.ringtoneService.play(AudioType.Ringing);
  }

  acceptCall(): void {
    this.ringtoneService.stop(AudioType.Ringing);
    this.dialogRef.close('accepted');
  }

  rejectCall(): void {
    this.ringtoneService.stop(AudioType.Ringing);
    this.dialogRef.close('rejected');
  }
}
